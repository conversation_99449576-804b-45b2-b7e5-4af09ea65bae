<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Profile Form Demo</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #2C33FF 0%, #20BDFF 50%, #00145B 100%);
            min-height: 100vh;
            padding: 40px 20px;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .demo-header h1 {
            color: #1a1a1a;
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .demo-header p {
            color: #666;
            font-size: 18px;
        }
        
        .demo-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .demo-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            border: 2px solid #e1e5e9;
            transition: all 0.3s ease;
        }
        
        .demo-card:hover {
            border-color: #2C33FF;
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(44, 51, 255, 0.15);
        }
        
        .demo-card h3 {
            color: #333;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .demo-card p {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .demo-btn {
            background: linear-gradient(135deg, #2C33FF 0%, #20BDFF 100%);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-block;
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(44, 51, 255, 0.4);
        }
        
        .instructions {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin-top: 30px;
        }
        
        .instructions h3 {
            color: #333;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 16px;
        }
        
        .instructions ol {
            color: #666;
            line-height: 1.6;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 12px 16px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 8px 0;
        }
        
        @media (max-width: 768px) {
            .demo-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>Business Profile Form Demo</h1>
            <p>Choose how you want to view the business profile form</p>
        </div>
        
        <div class="demo-options">
            <div class="demo-card">
                <h3>🌐 Standalone HTML Version</h3>
                <p>View the business profile form as a standalone HTML page with all styling and JavaScript included.</p>
                <a href="business-profile.html" class="demo-btn">View HTML Form</a>
            </div>
            
            <div class="demo-card">
                <h3>🚀 Laravel Application</h3>
                <p>Full Laravel application with database integration, validation, and proper MVC structure.</p>
                <a href="http://localhost:8000/business-profile" class="demo-btn">View Laravel Form</a>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🛠️ Laravel Setup Instructions</h3>
            <ol>
                <li>Navigate to the <code>business-profile-app</code> directory</li>
                <li>Run the following commands:</li>
            </ol>
            
            <div class="code">cd business-profile-app</div>
            <div class="code">php artisan migrate</div>
            <div class="code">php artisan storage:link</div>
            <div class="code">php artisan serve</div>
            
            <ol start="3">
                <li>Open your browser and go to <code>http://localhost:8000/business-profile</code></li>
                <li>Fill out the form and test the functionality</li>
            </ol>
        </div>
        
        <div class="instructions">
            <h3>📋 Form Features</h3>
            <ol>
                <li><strong>Business Logo Upload:</strong> Click the camera icon to upload a logo with live preview</li>
                <li><strong>Form Validation:</strong> All required fields are validated on both client and server side</li>
                <li><strong>Dynamic Address Fields:</strong> Click "+ Add Address" to add multiple business addresses</li>
                <li><strong>Database Storage:</strong> All form data is saved to SQLite database in Laravel version</li>
                <li><strong>Success Page:</strong> After submission, users see a summary of their submitted information</li>
                <li><strong>Responsive Design:</strong> Form works on desktop, tablet, and mobile devices</li>
            </ol>
        </div>
        
        <div class="instructions">
            <h3>🎨 Design Elements</h3>
            <ol>
                <li><strong>Color Scheme:</strong> Matches the original design with blue gradient background</li>
                <li><strong>Typography:</strong> Uses Inter font for modern, clean appearance</li>
                <li><strong>Interactive Elements:</strong> Hover effects, focus states, and smooth transitions</li>
                <li><strong>Form Layout:</strong> Clean, organized layout with proper spacing and visual hierarchy</li>
            </ol>
        </div>
    </div>
</body>
</html>

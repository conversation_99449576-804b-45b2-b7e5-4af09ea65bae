<?php

namespace App\Http\Controllers;

use App\Models\BusinessProfile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class BusinessProfileController extends Controller
{
    /**
     * Show the business profile form
     */
    public function create()
    {
        return view('business-profile.create');
    }
    
    /**
     * Store the business profile data
     */
    public function store(Request $request)
    {
        // Validate the form data
        $validator = Validator::make($request->all(), [
            'business_name' => 'required|string|max:255',
            'business_type' => 'required|string|max:255',
            'business_address' => 'required|string|max:500',
            'business_email' => 'required|email|max:255',
            'business_phone' => 'required|string|max:20',
            'service_name' => 'required|string|max:255',
            'employees_count' => 'nullable|integer|min:1',
            'business_website' => 'nullable|url|max:255',
            'business_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'additional_addresses' => 'nullable|array',
            'additional_addresses.*' => 'string|max:500',
            'add_payment' => 'nullable|boolean'
        ]);
        
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        
        $data = $request->all();
        
        // Handle logo upload
        if ($request->hasFile('business_logo')) {
            $logoPath = $request->file('business_logo')->store('business_logos', 'public');
            $data['business_logo'] = $logoPath;
        }
        
        // Save to database
        $businessProfile = BusinessProfile::create($data);

        return redirect()->route('business-profile.success', ['id' => $businessProfile->id])
            ->with('success', 'Business profile created successfully!');
    }
    
    /**
     * Show success page
     */
    public function success($id = null)
    {
        if ($id) {
            $profile = BusinessProfile::findOrFail($id);
        } else {
            $profile = session('business_profile');
            if (!$profile) {
                return redirect()->route('business-profile.create');
            }
        }

        return view('business-profile.success', compact('profile'));
    }
}

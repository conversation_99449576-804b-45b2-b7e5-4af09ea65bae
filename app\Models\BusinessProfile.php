<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BusinessProfile extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_name',
        'business_type',
        'business_address',
        'business_email',
        'business_phone',
        'service_name',
        'employees_count',
        'business_website',
        'business_logo',
        'additional_addresses',
        'add_payment',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'additional_addresses' => 'array',
        'add_payment' => 'boolean',
    ];

    /**
     * Get the logo URL
     */
    public function getLogoUrlAttribute()
    {
        if ($this->business_logo) {
            return asset('storage/' . $this->business_logo);
        }
        return null;
    }
}

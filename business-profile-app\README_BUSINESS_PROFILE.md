# Business Profile Form - Laravel Application

This Laravel application includes a comprehensive business profile form based on the "Bon Appointto" design.

## Features

### Form Fields
- **Business Logo** (Optional) - Image upload with preview
- **Business Name** (Required) - Text input
- **Business Type** (Required) - Text input  
- **Business Address** (Required) - Text input with "Add Address" functionality
- **Number of Employees** (Optional) - Number input
- **Business Website** (Optional) - URL input with globe icon
- **Business Email** (Required) - Email input with validation
- **Business Phone Number** (Required) - Phone input
- **Service Name** (Required) - Text input
- **Add Payment Information** - Checkbox option

### Design Features
- Modern gradient background matching the original design
- Clean white form container with rounded corners
- Proper form validation with error messages
- Success page showing submitted data
- Responsive design
- Interactive elements (logo upload preview, dynamic address fields)

## File Structure

```
business-profile-app/
├── app/
│   ├── Http/Controllers/
│   │   └── BusinessProfileController.php
│   └── Models/
│       └── BusinessProfile.php
├── database/migrations/
│   └── 2024_01_01_000000_create_business_profiles_table.php
├── resources/views/business-profile/
│   ├── create.blade.php
│   └── success.blade.php
└── routes/web.php
```

## Routes

- `GET /business-profile` - Show the business profile form
- `POST /business-profile` - Submit the business profile form
- `GET /business-profile/success/{id?}` - Show success page after submission

## Database

The application uses SQLite database (configured in .env) with a `business_profiles` table containing:
- business_name
- business_type  
- business_address
- business_email
- business_phone
- service_name
- employees_count (nullable)
- business_website (nullable)
- business_logo (nullable)
- additional_addresses (JSON)
- add_payment (boolean)

## How to Run

1. Navigate to the business-profile-app directory
2. Run `php artisan migrate` to create database tables
3. Run `php artisan serve` to start the development server
4. Visit `http://localhost:8000/business-profile` to see the form

## Form Validation

The form includes comprehensive validation:
- Required fields are enforced
- Email format validation
- URL format validation for website
- Image validation for logo upload (max 2MB, jpeg/png/jpg/gif)
- Phone number format validation

## File Upload

Business logos are stored in `storage/app/public/business_logos/` directory. Make sure to run `php artisan storage:link` to create the symbolic link for public access to uploaded files.

## Styling

The form uses the same color scheme as the original design:
- Primary Blue: #2C33FF
- Secondary Blue: #20BDFF  
- Dark Blue: #00145B
- Clean white form with subtle shadows and modern styling

<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\BusinessProfileController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// Business Profile Routes
Route::get('/business-profile', [BusinessProfileController::class, 'create'])->name('business-profile.create');
Route::post('/business-profile', [BusinessProfileController::class, 'store'])->name('business-profile.store');
Route::get('/business-profile/success', [BusinessProfileController::class, 'success'])->name('business-profile.success');

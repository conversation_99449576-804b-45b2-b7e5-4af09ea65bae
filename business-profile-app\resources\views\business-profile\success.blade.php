<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Profile Created - <PERSON></title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #2C33FF 0%, #20BDFF 50%, #00145B 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .success-container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            width: 100%;
            max-width: 600px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            text-align: center;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #2C33FF 0%, #20BDFF 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            color: white;
            font-size: 40px;
        }
        
        .success-header h1 {
            color: #1a1a1a;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .success-header p {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
        }
        
        .profile-summary {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 30px;
            text-align: left;
        }
        
        .profile-summary h3 {
            color: #333;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            text-align: center;
        }
        
        .profile-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .profile-item:last-child {
            border-bottom: none;
        }
        
        .profile-item .label {
            color: #666;
            font-weight: 500;
        }
        
        .profile-item .value {
            color: #333;
            font-weight: 400;
        }
        
        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #2C33FF 0%, #20BDFF 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(44, 51, 255, 0.4);
        }
        
        .btn-secondary {
            background: white;
            color: #2C33FF;
            border: 2px solid #2C33FF;
        }
        
        .btn-secondary:hover {
            background: #2C33FF;
            color: white;
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">✓</div>
        
        <div class="success-header">
            <h1>Business Profile Created Successfully!</h1>
            <p>Your business profile has been set up and is ready to use.</p>
        </div>
        
        <div class="profile-summary">
            <h3>Profile Summary</h3>
            
            <div class="profile-item">
                <span class="label">Business Name:</span>
                <span class="value">{{ is_array($profile) ? ($profile['business_name'] ?? 'N/A') : $profile->business_name }}</span>
            </div>
            
            <div class="profile-item">
                <span class="label">Business Type:</span>
                <span class="value">{{ is_array($profile) ? ($profile['business_type'] ?? 'N/A') : $profile->business_type }}</span>
            </div>
            
            <div class="profile-item">
                <span class="label">Email:</span>
                <span class="value">{{ is_array($profile) ? ($profile['business_email'] ?? 'N/A') : $profile->business_email }}</span>
            </div>
            
            <div class="profile-item">
                <span class="label">Phone:</span>
                <span class="value">{{ is_array($profile) ? ($profile['business_phone'] ?? 'N/A') : $profile->business_phone }}</span>
            </div>
            
            <div class="profile-item">
                <span class="label">Address:</span>
                <span class="value">{{ is_array($profile) ? ($profile['business_address'] ?? 'N/A') : $profile->business_address }}</span>
            </div>
            
            @if((is_array($profile) && isset($profile['business_website']) && $profile['business_website']) || (!is_array($profile) && $profile->business_website))
            <div class="profile-item">
                <span class="label">Website:</span>
                <span class="value">{{ is_array($profile) ? $profile['business_website'] : $profile->business_website }}</span>
            </div>
            @endif
            
            @if((is_array($profile) && isset($profile['employees_count']) && $profile['employees_count']) || (!is_array($profile) && $profile->employees_count))
            <div class="profile-item">
                <span class="label">Employees:</span>
                <span class="value">{{ is_array($profile) ? $profile['employees_count'] : $profile->employees_count }}</span>
            </div>
            @endif
            
            <div class="profile-item">
                <span class="label">Service Name:</span>
                <span class="value">{{ is_array($profile) ? ($profile['service_name'] ?? 'N/A') : $profile->service_name }}</span>
            </div>
            
            @if((is_array($profile) && isset($profile['add_payment']) && $profile['add_payment']) || (!is_array($profile) && $profile->add_payment))
            <div class="profile-item">
                <span class="label">Payment Info:</span>
                <span class="value">Enabled</span>
            </div>
            @endif
        </div>
        
        <div class="action-buttons">
            <a href="{{ route('business-profile.create') }}" class="btn btn-secondary">Edit Profile</a>
            <a href="#" class="btn btn-primary">Continue to Dashboard</a>
        </div>
    </div>
</body>
</html>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_profiles', function (Blueprint $table) {
            $table->id();
            $table->string('business_name');
            $table->string('business_type');
            $table->text('business_address');
            $table->string('business_email');
            $table->string('business_phone');
            $table->string('service_name');
            $table->integer('employees_count')->nullable();
            $table->string('business_website')->nullable();
            $table->string('business_logo')->nullable();
            $table->json('additional_addresses')->nullable();
            $table->boolean('add_payment')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_profiles');
    }
};

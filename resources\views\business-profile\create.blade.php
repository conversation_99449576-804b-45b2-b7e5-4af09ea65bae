<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set Your Business Profile - Bon Appointto</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #2C33FF 0%, #20BDFF 50%, #00145B 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .form-container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .form-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-header h1 {
            color: #1a1a1a;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .form-header p {
            color: #666;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-group label {
            display: block;
            color: #333;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .optional {
            color: #999;
            font-weight: 400;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            background: white;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #2C33FF;
            box-shadow: 0 0 0 3px rgba(44, 51, 255, 0.1);
        }
        
        .form-group input::placeholder {
            color: #999;
        }
        
        .logo-upload {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border: 2px dashed #e1e5e9;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: border-color 0.3s ease;
            background: #f8f9fa;
        }
        
        .logo-upload:hover {
            border-color: #2C33FF;
        }
        
        .logo-upload input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .logo-upload .upload-icon {
            color: #999;
            font-size: 24px;
        }
        
        .website-input {
            position: relative;
        }
        
        .website-input .globe-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }
        
        .website-input input {
            padding-left: 40px;
        }
        
        .add-address-btn {
            background: none;
            border: none;
            color: #2C33FF;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
        }
        
        .add-address-btn:hover {
            text-decoration: underline;
        }
        
        .payment-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 30px 0;
        }
        
        .payment-info input[type="checkbox"] {
            width: 20px;
            height: 20px;
            accent-color: #2C33FF;
        }
        
        .payment-info label {
            color: #333;
            font-size: 14px;
            margin: 0;
        }
        
        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #2C33FF 0%, #20BDFF 100%);
            color: white;
            border: none;
            padding: 16px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(44, 51, 255, 0.3);
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(44, 51, 255, 0.4);
        }
        
        .submit-btn:active {
            transform: translateY(0);
        }
        
        .error-message {
            color: #dc3545;
            font-size: 12px;
            margin-top: 4px;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <div class="form-header">
            <h1>Set Your Business Profile</h1>
            <p>Welcome To Bon Appointto</p>
        </div>
        
        @if(session('success'))
            <div class="success-message">
                {{ session('success') }}
            </div>
        @endif
        
        <form id="businessProfileForm" action="{{ route('business-profile.store') }}" method="POST" enctype="multipart/form-data">
            @csrf
            
            <div class="form-group">
                <label for="business_logo">Business Logo <span class="optional">(Optional)</span></label>
                <div class="logo-upload">
                    <input type="file" id="business_logo" name="business_logo" accept="image/*" value="{{ old('business_logo') }}">
                    <div class="upload-icon">📷</div>
                </div>
                @error('business_logo')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="business_name">Business Name</label>
                <input type="text" id="business_name" name="business_name" placeholder="Enter your business name" value="{{ old('business_name') }}" required>
                @error('business_name')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="business_type">Business Type</label>
                <input type="text" id="business_type" name="business_type" placeholder="Enter your business type" value="{{ old('business_type') }}" required>
                @error('business_type')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="business_address">Business Address</label>
                <input type="text" id="business_address" name="business_address" placeholder="Enter your business location" value="{{ old('business_address') }}" required>
                <button type="button" class="add-address-btn">
                    <span>+</span> Add Address
                </button>
                @error('business_address')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="employees_count">No of Employees</label>
                <input type="number" id="employees_count" name="employees_count" placeholder="Enter number of employees" value="{{ old('employees_count') }}" min="1">
                @error('employees_count')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="business_website">Business Website</label>
                <div class="website-input">
                    <span class="globe-icon">🌐</span>
                    <input type="url" id="business_website" name="business_website" placeholder="Enter your website URL" value="{{ old('business_website') }}">
                </div>
                @error('business_website')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="business_email">Business Email</label>
                <input type="email" id="business_email" name="business_email" placeholder="Enter your business email" value="{{ old('business_email') }}" required>
                @error('business_email')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="business_phone">Business Phone Number</label>
                <input type="tel" id="business_phone" name="business_phone" placeholder="123 457 789" value="{{ old('business_phone') }}" required>
                @error('business_phone')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="service_name">Service Name</label>
                <input type="text" id="service_name" name="service_name" placeholder="Enter service name" value="{{ old('service_name') }}" required>
                @error('service_name')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="payment-info">
                <input type="checkbox" id="add_payment" name="add_payment" value="1" {{ old('add_payment') ? 'checked' : '' }}>
                <label for="add_payment">Add Payment Information</label>
            </div>
            
            <button type="submit" class="submit-btn">Sign Up</button>
        </form>
    </div>
    
    <script>
        // Handle logo upload preview
        document.getElementById('business_logo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const uploadIcon = document.querySelector('.upload-icon');
                    uploadIcon.innerHTML = `<img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">`;
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Add address functionality
        document.querySelector('.add-address-btn').addEventListener('click', function() {
            const addressGroup = this.parentElement;
            const newAddressInput = document.createElement('input');
            newAddressInput.type = 'text';
            newAddressInput.name = 'additional_addresses[]';
            newAddressInput.placeholder = 'Enter additional address';
            newAddressInput.style.marginTop = '8px';
            newAddressInput.className = 'form-group input';
            newAddressInput.style.width = '100%';
            newAddressInput.style.padding = '12px 16px';
            newAddressInput.style.border = '1px solid #e1e5e9';
            newAddressInput.style.borderRadius = '8px';
            newAddressInput.style.fontSize = '16px';
            
            addressGroup.appendChild(newAddressInput);
        });
    </script>
</body>
</html>

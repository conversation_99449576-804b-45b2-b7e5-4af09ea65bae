<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\BusinessProfileController;

Route::get('/', function () {
    return redirect()->route('business-profile.create');
});

// Business Profile Routes
Route::get('/business-profile', [BusinessProfileController::class, 'create'])->name('business-profile.create');
Route::post('/business-profile', [BusinessProfileController::class, 'store'])->name('business-profile.store');
Route::get('/business-profile/success/{id?}', [BusinessProfileController::class, 'success'])->name('business-profile.success');
